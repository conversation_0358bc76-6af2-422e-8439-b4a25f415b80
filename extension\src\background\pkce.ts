function base64url(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer)
  let binary = ''
  for (let i = 0; i < bytes.byteLength; i++) binary += String.fromCharCode(bytes[i])
  return btoa(binary).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '')
}

export async function generatePKCE() {
  const random = crypto.getRandomValues(new Uint8Array(32))
  const verifier = base64url(random.buffer)
  const encoder = new TextEncoder()
  const data = encoder.encode(verifier)
  const digest = await crypto.subtle.digest('SHA-256', data)
  const challenge = base64url(digest)
  return { verifier, challenge }
}

export function randomState(length = 32) {
  const arr = crypto.getRandomValues(new Uint8Array(length))
  return Array.from(arr, (b) => ('0' + b.toString(16)).slice(-2)).join('')
}
