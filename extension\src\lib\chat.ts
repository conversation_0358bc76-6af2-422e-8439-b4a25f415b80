import { BACKEND_BASE_URL } from './config'
import type { Conversation, ChatMessage } from '../types'

export async function listConversations(limit = 100): Promise<Conversation[]> {
  const res = await fetch(`${BACKEND_BASE_URL}/conversations?limit=${limit}`)
  if (!res.ok) throw new Error(`listConversations ${res.status}`)
  const j = await res.json()
  return j.conversations as Conversation[]
}

export async function listMessages(conversationId: string, limit = 500): Promise<ChatMessage[]> {
  const res = await fetch(`${BACKEND_BASE_URL}/conversations/${encodeURIComponent(conversationId)}/messages?limit=${limit}`)
  if (!res.ok) throw new Error(`listMessages ${res.status}`)
  const j = await res.json()
  return j.messages as ChatMessage[]
}

export type ChatAskInput = {
  conversationId?: string
  message: string
  title?: string | null
  accountId?: string | null
  gmailAccessToken?: string | null
}

export type ChatAskEvent =
  | { type: 'meta'; conversationId: string }
  | { type: 'delta'; text: string }
  | { type: 'done' }
  | { type: 'error'; message: string }
  | { type: 'tool'; phase: 'start' | 'end' | 'error'; name: string; args?: unknown; result_excerpt?: string; error?: string }

export async function chatAsk(input: ChatAskInput, onEvent: (e: ChatAskEvent) => void) {
  const res = await fetch(`${BACKEND_BASE_URL}/chat/ask`, {
    method: 'POST',
    headers: { 'content-type': 'application/json' },
    body: JSON.stringify(input),
  })
  if (!res.ok || !res.body) {
    const detail = await res.text().catch(() => '')
    onEvent({ type: 'error', message: `HTTP ${res.status} ${detail}` })
    onEvent({ type: 'done' })
    return
  }
  const reader = res.body.getReader()
  const decoder = new TextDecoder()
  let buffer = ''
  while (true) {
    const { value, done } = await reader.read()
    if (done) break
    buffer += decoder.decode(value, { stream: true })
    const events = buffer.split('\n\n')
    buffer = events.pop() || ''
    for (const evt of events) {
      for (const line of evt.split('\n')) {
        const trimmed = line.trim()
        if (!trimmed.startsWith('data:')) continue
        const jsonStr = trimmed.slice(5).trim()
        if (!jsonStr) continue
        try {
          const obj = JSON.parse(jsonStr)
          if (obj && typeof obj.type === 'string') {
            onEvent(obj as ChatAskEvent)
          }
        } catch {
          // ignore
        }
      }
    }
  }
  onEvent({ type: 'done' })
}
