export type CryptoEnv = {
  ENCRYPTION_KEY: string
}

const textEncoder = new TextEncoder()
const textDecoder = new TextDecoder()

function toBytes(key: string): Uint8Array {
  try {
    const b = atob(key)
    return Uint8Array.from(b, (c) => c.charCodeAt(0))
  } catch {}
  const hex = key.replace(/[^0-9a-f]/gi, '')
  const out = new Uint8Array(hex.length / 2)
  for (let i = 0; i < out.length; i++) out[i] = parseInt(hex.substr(i * 2, 2), 16)
  return out
}

export async function importAesKey(env: CryptoEnv) {
  const bytes = toBytes(env.ENCRYPTION_KEY)
  if (bytes.length !== 32) throw new Error('ENCRYPTION_KEY must be 32 bytes (base64 or hex)')
  return crypto.subtle.importKey('raw', bytes, 'AES-GCM', false, ['encrypt', 'decrypt'])
}

export function b64(bytes: Uint8Array) {
  let s = ''
  for (const b of bytes) s += String.fromCharCode(b)
  return btoa(s)
}

export function b64ToBytes(s: string) {
  const bin = atob(s)
  const out = new Uint8Array(bin.length)
  for (let i = 0; i < bin.length; i++) out[i] = bin.charCodeAt(i)
  return out
}

export async function encrypt(env: CryptoEnv, plaintext: string) {
  const key = await importAesKey(env)
  const iv = crypto.getRandomValues(new Uint8Array(12))
  const ct = new Uint8Array(
    await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, key, textEncoder.encode(plaintext)),
  )
  const out = new Uint8Array(iv.length + ct.length)
  out.set(iv, 0)
  out.set(ct, iv.length)
  return b64(out)
}

export async function decrypt(env: CryptoEnv, payloadB64: string) {
  const key = await importAesKey(env)
  const data = b64ToBytes(payloadB64)
  const iv = data.slice(0, 12)
  const ct = data.slice(12)
  const pt = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, key, ct)
  return textDecoder.decode(pt)
}
