{
  // Cloudflare Wrangler configuration (JSONC)
  // Docs: https://developers.cloudflare.com/workers/wrangler/configuration/
  "name": "ai-email-agent-worker",
  "main": "src/index.ts",
  "compatibility_date": "2024-12-01",
  "compatibility_flags": ["nodejs_compat"],

  // Dev server config
  "dev": {
    "port": 8787
  },

  // Workers AI binding (optional, enables env.AI)
  "ai": {
    "binding": "AI"
  },

  // Durable Objects bindings
  "durable_objects": {
    "bindings": [
      { "name": "TOKEN_VAULT", "class_name": "TokenVault" },
      { "name": "GmailMCP", "class_name": "GmailMCP" }
    ]
  },

  // Durable Objects migrations
  // Note: use new_sqlite_classes for DO storage-backed classes
  "migrations": [
    {
      "tag": "v1",
      "new_sqlite_classes": ["TokenVault"]
    },
    {
      "tag": "v2",
      "new_sqlite_classes": ["GmailMCP"]
    }
  ],

  // Non-secret vars
  "vars": {
    "AI_GATEWAY_URL": "https://gateway.ai.cloudflare.com/v1/6fd3a49ffbcade6766f0c5cc2e70e888/extension/"
  },

  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "email_agent",
      "database_id": "01f8c23e-fc0a-4e3d-bdd1-5c7573a0441a"
    }
  ]
}
