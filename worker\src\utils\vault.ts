export class TokenVault {
  state: DurableObjectState
  constructor(state: DurableObjectState) {
    this.state = state
  }
  async fetch(request: Request) {
    const url = new URL(request.url)
    if (request.method === 'PUT' && url.pathname === '/put') {
      const body = await request.json<any>()
      await this.state.storage.put('value', body.value)
      return new Response(null, { status: 204 })
    }
    if (request.method === 'GET' && url.pathname === '/get') {
      const value = await this.state.storage.get<string>('value')
      if (!value) return new Response('not found', { status: 404 })
      return Response.json({ value })
    }
    if (request.method === 'DELETE' && url.pathname === '/del') {
      await this.state.storage.delete('value')
      return new Response(null, { status: 204 })
    }
    return new Response('Not found', { status: 404 })
  }
}

export async function vaultPut(ns: DurableObjectNamespace, id: string, value: string) {
  const stub = ns.get(ns.idFromName(id))
  const r = await stub.fetch('https://vault/put', { method: 'PUT', body: JSON.stringify({ value }) })
  if (!r.ok) throw new Error('vault put failed')
}

export async function vaultGet(ns: DurableObjectNamespace, id: string) {
  const stub = ns.get(ns.idFromName(id))
  const r = await stub.fetch('https://vault/get')
  if (r.status === 404) return null as string | null
  if (!r.ok) throw new Error('vault get failed')
  const j = await r.json<any>()
  return j.value as string
}

export async function vaultDel(ns: DurableObjectNamespace, id: string) {
  const stub = ns.get(ns.idFromName(id))
  await stub.fetch('https://vault/del', { method: 'DELETE' })
}
