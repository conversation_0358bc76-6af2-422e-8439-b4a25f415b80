import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js"
import { McpAgent } from "agents/mcp"
import { z } from "zod"
import { ensureSchema, insertMessage, upsertConversation } from "../utils/d1"
import type { D1Database } from 'cloudflare:workers'

export type WorkerEnv = {
  GmailMCP: DurableObjectNamespace
  // Workers AI binding
  AI?: any
  // D1 database binding for transcripts
  DB?: D1Database
  AI_GATEWAY_URL?: string
  AI_GATEWAY_TOKEN?: string
  GOOGLE_AI_STUDIO_API_KEY?: string
}

type State = {
  // Placeholder for future per-agent state/memory
  lastUsedAt?: number
}

async function gmailFetch<T>(accessToken: string, path: string, init?: RequestInit): Promise<T> {
  const url = new URL(`https://gmail.googleapis.com/gmail/v1/${path}`)
  const res = await fetch(url.toString(), {
    ...init,
    headers: {
      ...(init?.headers || {}),
      Authorization: `Bearer ${accessToken}`,
    },
  })
  if (!res.ok) {
    const text = await res.text()
    throw new Error(`Gmail API error ${res.status}: ${text}`)
  }
  return (await res.json()) as T
}

function ensureString(v: unknown, name: string): string {
  if (!v || typeof v !== 'string') throw new Error(`${name} is required`)
  return v
}

export class GmailMCP extends McpAgent<WorkerEnv, State, {}> {
  server = new McpServer({ name: "GmailMCP", version: "0.1.0" })

  initialState: State = {}

  async init() {
    // Initialize D1 schema if available
    if (this.env.DB) {
      await ensureSchema(this.env.DB)
    }
    // Tools: listThreads
    this.server.tool(
      "gmail.listThreads",
      "List Gmail threads using Gmail API (read-only)",
      {
        accessToken: z.string().describe("Google OAuth access token for the target account"),
        // Treat empty string as undefined
        q: z.preprocess((v) => (typeof v === 'string' && v.trim() === '' ? undefined : v), z.string().optional()).describe("Gmail search query"),
        // Accept array or CSV string; empty string becomes undefined
        labelIds: z.preprocess((v) => {
          if (v == null || v === '') return undefined
          if (Array.isArray(v)) return v
          if (typeof v === 'string') return v.split(',').map((s) => s.trim()).filter(Boolean)
          return v
        }, z.array(z.string()).optional()).describe("Filter by label IDs"),
        pageToken: z.preprocess((v) => (v === '' ? undefined : v), z.string().optional()).describe("Pagination token"),
        includeSpamTrash: z.preprocess((v) => {
          if (v === '') return undefined
          if (v === 'true') return true
          if (v === 'false') return false
          return v
        }, z.boolean().optional()),
        maxResults: z.preprocess((v) => {
          if (v === '') return undefined
          if (typeof v === 'string') {
            const n = parseInt(v, 10)
            return Number.isNaN(n) ? v : n
          }
          return v
        }, z.number().int().min(1).max(100).optional()),
      },
      async ({ accessToken, q, labelIds, pageToken, includeSpamTrash, maxResults }) => {
        const params = new URLSearchParams()
        if (q) params.set('q', q)
        if (labelIds?.length) for (const id of labelIds) params.append('labelIds', id)
        if (pageToken) params.set('pageToken', pageToken)
        if (includeSpamTrash != null) params.set('includeSpamTrash', String(includeSpamTrash))
        if (maxResults != null) params.set('maxResults', String(maxResults))
        const data = await gmailFetch<any>(accessToken, `users/me/threads?${params.toString()}`)
        // Persist transcript (conversation keyed by search)
        try {
          const convId = `search:${q || 'inbox'}`
          if (this.env.DB) await upsertConversation(this.env.DB, convId, `Search: ${q || 'inbox'}`, null)
          if (this.env.DB) await insertMessage(this.env.DB, {
            conversation_id: convId,
            role: 'tool',
            tool_name: 'gmail.listThreads',
            args_json: JSON.stringify({ q, labelIds, pageToken, includeSpamTrash, maxResults }),
            content: null,
            result_excerpt: JSON.stringify({ resultCount: data.threads?.length || 0 }).slice(0, 1000),
          })
        } catch {}
        return {
          content: [
            { type: 'text', text: JSON.stringify(data, null, 2) },
          ],
        }
      }
    )

    // Tools: listMessages (filter by read/unread using Gmail search operators)
    this.server.tool(
      "gmail.listMessages",
      "List Gmail messages with optional read/unread filter (read-only)",
      {
        accessToken: z.string().describe("Google OAuth access token for the target account"),
        q: z.preprocess((v) => (typeof v === 'string' && v.trim() === '' ? undefined : v), z.string().optional()).describe("Gmail search query (will be combined with is:unread / is:read if provided)"),
        unread: z.preprocess((v) => {
          if (v === '') return undefined
          if (v === 'true') return true
          if (v === 'false') return false
          return v
        }, z.boolean().optional()).describe("If true, filter unread messages; if false, filter read messages"),
        labelIds: z.preprocess((v) => {
          if (v == null || v === '') return undefined
          if (Array.isArray(v)) return v
          if (typeof v === 'string') return v.split(',').map((s) => s.trim()).filter(Boolean)
          return v
        }, z.array(z.string()).optional()).describe("Filter by label IDs"),
        pageToken: z.preprocess((v) => (v === '' ? undefined : v), z.string().optional()).describe("Pagination token"),
        includeSpamTrash: z.preprocess((v) => {
          if (v === '') return undefined
          if (v === 'true') return true
          if (v === 'false') return false
          return v
        }, z.boolean().optional()),
        maxResults: z.preprocess((v) => {
          if (v === '') return undefined
          if (typeof v === 'string') {
            const n = parseInt(v, 10)
            return Number.isNaN(n) ? v : n
          }
          return v
        }, z.number().int().min(1).max(100).optional()),
      },
      async ({ accessToken, q, unread, labelIds, pageToken, includeSpamTrash, maxResults }) => {
        const params = new URLSearchParams()
        // Merge unread/read operator into query
        let query = q || ''
        if (unread === true) query = (query ? `${query} ` : '') + 'is:unread'
        if (unread === false) query = (query ? `${query} ` : '') + 'is:read'
        if (query) params.set('q', query)
        if (labelIds?.length) for (const id of labelIds) params.append('labelIds', id)
        if (pageToken) params.set('pageToken', pageToken)
        if (includeSpamTrash != null) params.set('includeSpamTrash', String(includeSpamTrash))
        if (maxResults != null) params.set('maxResults', String(maxResults))
        const data = await gmailFetch<any>(accessToken, `users/me/messages?${params.toString()}`)
        // Persist transcript (conversation keyed by query/read-state)
        try {
          const convId = `messages:${unread === true ? 'unread' : unread === false ? 'read' : (q || 'all')}`
          if (this.env.DB) await upsertConversation(this.env.DB, convId, `Messages: ${unread === true ? 'Unread' : unread === false ? 'Read' : (q || 'All')}`, null)
          if (this.env.DB) await insertMessage(this.env.DB, {
            conversation_id: convId,
            role: 'tool',
            tool_name: 'gmail.listMessages',
            args_json: JSON.stringify({ q, unread, labelIds, pageToken, includeSpamTrash, maxResults }),
            content: null,
            result_excerpt: JSON.stringify({ resultCount: data.messages?.length || 0 }).slice(0, 1000),
          })
        } catch {}
        return {
          content: [
            { type: 'text', text: JSON.stringify(data, null, 2) },
          ],
        }
      }
    )

    // Tools: getThread
    this.server.tool(
      "gmail.getThread",
      "Get a Gmail thread (read-only)",
      {
        accessToken: z.string(),
        threadId: z.string(),
        format: z.enum(["minimal", "full", "metadata"]).optional().default("full"),
      },
      async ({ accessToken, threadId, format }) => {
        const data = await gmailFetch<any>(accessToken, `users/me/threads/${threadId}?format=${format}`)
        try {
          const convId = `thread:${threadId}`
          if (this.env.DB) await upsertConversation(this.env.DB, convId, `Thread ${threadId}`, null)
          if (this.env.DB) await insertMessage(this.env.DB, {
            conversation_id: convId,
            role: 'tool',
            tool_name: 'gmail.getThread',
            args_json: JSON.stringify({ threadId, format }),
            content: null,
            result_excerpt: JSON.stringify({ messageCount: data.messages?.length || 0 }).slice(0, 1000),
          })
        } catch {}
        return { content: [{ type: 'text', text: JSON.stringify(data, null, 2) }] }
      }
    )

    // Tools: getMessage
    this.server.tool(
      "gmail.getMessage",
      "Get a Gmail message (read-only)",
      {
        accessToken: z.string(),
        id: z.string(),
        format: z.enum(["minimal", "full", "raw", "metadata"]).optional().default("full"),
      },
      async ({ accessToken, id, format }) => {
        const data = await gmailFetch<any>(accessToken, `users/me/messages/${id}?format=${format}`)
        try {
          const convId = `message:${id}`
          if (this.env.DB) await upsertConversation(this.env.DB, convId, `Message ${id}`, null)
          if (this.env.DB) await insertMessage(this.env.DB, {
            conversation_id: convId,
            role: 'tool',
            tool_name: 'gmail.getMessage',
            args_json: JSON.stringify({ id, format }),
            content: null,
            result_excerpt: JSON.stringify({ payload: !!data.payload }).slice(0, 1000),
          })
        } catch {}
        return { content: [{ type: 'text', text: JSON.stringify(data, null, 2) }] }
      }
    )

    // Tools: getAttachment
    this.server.tool(
      "gmail.getAttachment",
      "Get a Gmail attachment (read-only)",
      {
        accessToken: z.string(),
        messageId: z.string(),
        attachmentId: z.string(),
      },
      async ({ accessToken, messageId, attachmentId }) => {
        const data = await gmailFetch<any>(accessToken, `users/me/messages/${messageId}/attachments/${attachmentId}`)
        try {
          const convId = `message:${messageId}`
          if (this.env.DB) await upsertConversation(this.env.DB, convId, `Message ${messageId}`, null)
          if (this.env.DB) await insertMessage(this.env.DB, {
            conversation_id: convId,
            role: 'tool',
            tool_name: 'gmail.getAttachment',
            args_json: JSON.stringify({ messageId, attachmentId }),
            content: null,
            result_excerpt: JSON.stringify({ dataLen: data.data?.length || 0 }).slice(0, 1000),
          })
        } catch {}
        return { content: [{ type: 'text', text: JSON.stringify(data, null, 2) }] }
      }
    )

    // Tool: summarize.thread (refactored to use env.AI.gateway(...), with fetch fallback)
    this.server.tool(
      "summarize.thread",
      "Summarize a Gmail thread using AI (Gemini via Cloudflare AI Gateway)",
      {
        accessToken: z.string(),
        threadId: z.string(),
        model: z.string().optional().default("google-ai-studio/gemini-2.5-flash-lite"),
      },
      async ({ accessToken, threadId, model }) => {
        // Fetch thread content first
        const thread = await gmailFetch<any>(accessToken, `users/me/threads/${threadId}?format=full`)
        const messages: string[] = []
        try {
          for (const m of thread.messages ?? []) {
            const parts = m.payload?.parts ?? []
            const body = m.payload?.body?.data
            if (body) messages.push(atob(body.replace(/-/g, '+').replace(/_/g, '/')))
            for (const p of parts) {
              if (p.mimeType === 'text/plain' && p.body?.data) {
                messages.push(atob(p.body.data.replace(/-/g, '+').replace(/_/g, '/')))
              }
            }
          }
        } catch {}

        const prompt = `Summarize the following email thread succinctly with key points and action items:\n\n${messages.join('\n\n---\n\n').slice(0, 10000)}`

        // Preferred path: Workers AI binding + Gateway name "extension" (try streaming)
        if (this.env.AI) {
          try {
            const gatewayName = 'extension' // per user gateway name
            const providerHeaders: Record<string, string> = {}
            if (this.env.GOOGLE_AI_STUDIO_API_KEY) {
              providerHeaders['Authorization'] = `Bearer ${this.env.GOOGLE_AI_STUDIO_API_KEY}`
              providerHeaders['x-goog-api-key'] = this.env.GOOGLE_AI_STUDIO_API_KEY
            }
            const gwResp: Response = await this.env.AI.gateway(gatewayName).run({
              provider: 'compat',
              endpoint: 'chat/completions',
              headers: providerHeaders,
              query: {
                model,
                messages: [
                  { role: 'system', content: 'You are an expert email assistant.' },
                  { role: 'user', content: prompt },
                ],
              },
              // Hints for streaming (implementation dependent under the hood)
              stream: true,
            } as any)

            if (gwResp) {
              const ct = gwResp.headers.get('content-type') || ''
              // If gateway returns SSE for streaming
              if (ct.includes('text/event-stream') && gwResp.body) {
                const reader = gwResp.body.getReader()
                const decoder = new TextDecoder()
                let buffer = ''
                let out = ''
                while (true) {
                  const { value, done } = await reader.read()
                  if (done) break
                  buffer += decoder.decode(value, { stream: true })
                  // Split SSE events by double newline
                  const events = buffer.split('\n\n')
                  // Keep the last partial in buffer
                  buffer = events.pop() || ''
                  for (const evt of events) {
                    // Each event may contain multiple lines; parse `data:` lines
                    for (const line of evt.split('\n')) {
                      const trimmed = line.trim()
                      if (!trimmed.startsWith('data:')) continue
                      const jsonStr = trimmed.slice(5).trim()
                      if (!jsonStr) continue
                      try {
                        const obj = JSON.parse(jsonStr)
                        if (obj?.type === 'universal.stream' && obj?.response?.response) {
                          out += String(obj.response.response)
                          // Note: true incremental streaming to the MCP client will be wired in Phase 5 UI.
                        }
                      } catch {}
                    }
                  }
                }
                if (out) {
                  // Persist summary
                  try {
                    const convId = `thread:${threadId}`
                    if (this.env.DB) await upsertConversation(this.env.DB, convId, `Thread ${threadId}`, null)
                    if (this.env.DB) await insertMessage(this.env.DB, {
                      conversation_id: convId,
                      role: 'assistant',
                      tool_name: 'summarize.thread',
                      args_json: JSON.stringify({ model }),
                      content: out,
                      result_excerpt: out.slice(0, 1000),
                    })
                  } catch {}
                  return { content: [{ type: 'text', text: out }] }
                }
                // If no stream chunks parsed, fall back to JSON parse below
              }
              // Non-SSE (JSON) fallback on env.AI
              if (gwResp.ok) {
                const j = await gwResp.json<any>()
                const text = j?.choices?.[0]?.message?.content
                  ?? j?.[0]?.response?.result?.response
                  ?? JSON.stringify(j).slice(0, 4000)
                try {
                  const convId = `thread:${threadId}`
                  if (this.env.DB) await upsertConversation(this.env.DB, convId, `Thread ${threadId}`, null)
                  if (this.env.DB) await insertMessage(this.env.DB, {
                    conversation_id: convId,
                    role: 'assistant',
                    tool_name: 'summarize.thread',
                    args_json: JSON.stringify({ model }),
                    content: text,
                    result_excerpt: text.slice(0, 1000),
                  })
                } catch {}
                return { content: [{ type: 'text', text }] }
              } else {
                const detail = await gwResp.text()
                return { content: [{ type: 'text', text: `AI Gateway (env.AI) error ${gwResp.status}: ${detail}` }] }
              }
            }
          } catch (e: any) {
            // fallthrough to HTTP fetch fallback
          }
        }

        // Fallback: direct Universal Endpoint HTTP fetch using AI_GATEWAY_URL (+ optional AI_GATEWAY_TOKEN for auth)
        const gateway = this.env.AI_GATEWAY_URL
        if (!gateway) {
          return { content: [{ type: 'text', text: '[dev] AI not configured; please set Workers AI binding or AI_GATEWAY_URL' }] }
        }
        const providerHeaders: Record<string, string> = {}
        if (this.env.GOOGLE_AI_STUDIO_API_KEY) {
          providerHeaders['Authorization'] = `Bearer ${this.env.GOOGLE_AI_STUDIO_API_KEY}`
          providerHeaders['x-goog-api-key'] = this.env.GOOGLE_AI_STUDIO_API_KEY
        }
        const payload = [
          {
            provider: 'compat',
            endpoint: 'chat/completions',
            headers: providerHeaders,
            query: {
              model,
              messages: [
                { role: 'system', content: 'You are an expert email assistant.' },
                { role: 'user', content: prompt },
              ],
            },
          },
        ]
        const resp = await fetch(gateway, {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
            // Ask for SSE if gateway supports streaming on universal endpoint
            'accept': 'text/event-stream, application/json',
            ...(this.env.AI_GATEWAY_TOKEN ? { 'cf-aig-authorization': `Bearer ${this.env.AI_GATEWAY_TOKEN}` } : {}),
          },
          body: JSON.stringify(payload),
        })
        if (!resp.ok) {
          const detail = await resp.text()
          return { content: [{ type: 'text', text: `AI Gateway error ${resp.status}: ${detail}` }] }
        }
        const ct = resp.headers.get('content-type') || ''
        if (ct.includes('text/event-stream') && resp.body) {
          // Parse SSE chunks
          const reader = resp.body.getReader()
          const decoder = new TextDecoder()
          let buffer = ''
          let out = ''
          while (true) {
            const { value, done } = await reader.read()
            if (done) break
            buffer += decoder.decode(value, { stream: true })
            const events = buffer.split('\n\n')
            buffer = events.pop() || ''
            for (const evt of events) {
              for (const line of evt.split('\n')) {
                const trimmed = line.trim()
                if (!trimmed.startsWith('data:')) continue
                const jsonStr = trimmed.slice(5).trim()
                if (!jsonStr) continue
                try {
                  const obj = JSON.parse(jsonStr)
                  if (obj?.type === 'universal.stream' && obj?.response?.response) {
                    out += String(obj.response.response)
                  }
                } catch {}
              }
            }
          }
          if (out) {
            try {
              const convId = `thread:${threadId}`
              if (this.env.DB) await upsertConversation(this.env.DB, convId, `Thread ${threadId}`, null)
              if (this.env.DB) await insertMessage(this.env.DB, {
                conversation_id: convId,
                role: 'assistant',
                tool_name: 'summarize.thread',
                args_json: JSON.stringify({ model }),
                content: out,
                result_excerpt: out.slice(0, 1000),
              })
            } catch {}
            return { content: [{ type: 'text', text: out }] }
          }
        }
        // JSON fallback
        const j = await resp.json<any>()
        const text = j?.[0]?.response?.result?.response
          ?? j?.choices?.[0]?.message?.content
          ?? JSON.stringify(j).slice(0, 4000)
        try {
          const convId = `thread:${threadId}`
          if (this.env.DB) await upsertConversation(this.env.DB, convId, `Thread ${threadId}`, null)
          if (this.env.DB) await insertMessage(this.env.DB, {
            conversation_id: convId,
            role: 'assistant',
            tool_name: 'summarize.thread',
            args_json: JSON.stringify({ model }),
            content: text,
            result_excerpt: text.slice(0, 1000),
          })
        } catch {}
        return { content: [{ type: 'text', text }] }
      }
    )

    // ---------------------------
    // Phase 4.1 — Action Tools + HITL
    // ---------------------------

    // Helper: build a confirm-required response
    const confirmRequired = (tool: string, args: any, message: string) => ({
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            type: 'CONFIRM_REQUIRED',
            tool,
            args,
            message,
          }, null, 2),
        },
      ],
    })

    // Helper: safe fetch for endpoints that may return 204/empty
    async function gmailFetchNoContent(accessToken: string, path: string, init?: RequestInit): Promise<{ ok: true }>{
      const url = new URL(`https://gmail.googleapis.com/gmail/v1/${path}`)
      const res = await fetch(url.toString(), {
        ...init,
        headers: {
          ...(init?.headers || {}),
          Authorization: `Bearer ${accessToken}`,
          'content-type': 'application/json',
        },
      })
      if (!res.ok) {
        const text = await res.text()
        throw new Error(`Gmail API error ${res.status}: ${text}`)
      }
      return { ok: true as const }
    }

    // Helper: label constants (system label IDs)
    const SYS = {
      INBOX: 'INBOX',
      UNREAD: 'UNREAD',
      SPAM: 'SPAM',
      TRASH: 'TRASH',
    } as const

    // gmail.modifyLabels — generic label modify on a message
    this.server.tool(
      "gmail.modifyLabels",
      "Modify labels on a Gmail message (requires gmail.modify; HITL confirm)",
      {
        accessToken: z.string(),
        id: z.string().describe('Message ID'),
        addLabelIds: z.array(z.string()).optional(),
        removeLabelIds: z.array(z.string()).optional(),
        confirm: z.boolean().optional().default(false),
      },
      async ({ accessToken, id, addLabelIds, removeLabelIds, confirm }) => {
        if (confirm !== true) {
          return confirmRequired('gmail.modifyLabels', { id, addLabelIds, removeLabelIds }, 'This action will change labels on the message. Please confirm to proceed.')
        }
        try {
          const data = await gmailFetch<any>(accessToken, `users/me/messages/${id}/modify`, {
            method: 'POST',
            headers: { 'content-type': 'application/json' },
            body: JSON.stringify({ addLabelIds: addLabelIds ?? [], removeLabelIds: removeLabelIds ?? [] }),
          })
          // Persist transcript
          try {
            const convId = `message:${id}`
            if (this.env.DB) await upsertConversation(this.env.DB, convId, `Message ${id}`, null)
            if (this.env.DB) await insertMessage(this.env.DB, {
              conversation_id: convId,
              role: 'tool',
              tool_name: 'gmail.modifyLabels',
              args_json: JSON.stringify({ id, addLabelIds, removeLabelIds }),
              content: null,
              result_excerpt: JSON.stringify({ labelIds: data.labelIds }).slice(0, 1000),
            })
          } catch {}
          return { content: [{ type: 'text', text: JSON.stringify(data, null, 2) }] }
        } catch (e: any) {
          const msg = String(e?.message || e)
          if (msg.includes('403')) {
            return { content: [{ type: 'text', text: `ERR_INSUFFICIENT_SCOPE: gmail.modify or gmail.labels required. Detail: ${msg}` }] }
          }
          return { content: [{ type: 'text', text: `Error: ${msg}` }] }
        }
      }
    )

    // gmail.markReadUnread — toggle UNREAD label
    this.server.tool(
      "gmail.markReadUnread",
      "Mark a Gmail message as read/unread (requires gmail.modify; HITL confirm)",
      {
        accessToken: z.string(),
        id: z.string().describe('Message ID'),
        unread: z.boolean().describe('If true => mark as UNREAD, else remove UNREAD'),
        confirm: z.boolean().optional().default(false),
      },
      async ({ accessToken, id, unread, confirm }) => {
        if (confirm !== true) {
          return confirmRequired('gmail.markReadUnread', { id, unread }, `This will mark the message as ${unread ? 'UNREAD' : 'READ'}. Please confirm.`)
        }
        const add = unread ? [SYS.UNREAD] : []
        const remove = unread ? [] : [SYS.UNREAD]
        try {
          const data = await gmailFetch<any>(accessToken, `users/me/messages/${id}/modify`, {
            method: 'POST',
            headers: { 'content-type': 'application/json' },
            body: JSON.stringify({ addLabelIds: add, removeLabelIds: remove }),
          })
          try {
            const convId = `message:${id}`
            if (this.env.DB) await upsertConversation(this.env.DB, convId, `Message ${id}`, null)
            if (this.env.DB) await insertMessage(this.env.DB, {
              conversation_id: convId,
              role: 'tool',
              tool_name: 'gmail.markReadUnread',
              args_json: JSON.stringify({ id, unread }),
              content: null,
              result_excerpt: JSON.stringify({ labelIds: data.labelIds }).slice(0, 1000),
            })
          } catch {}
          return { content: [{ type: 'text', text: JSON.stringify(data, null, 2) }] }
        } catch (e: any) {
          const msg = String(e?.message || e)
          if (msg.includes('403')) {
            return { content: [{ type: 'text', text: `ERR_INSUFFICIENT_SCOPE: gmail.modify required. Detail: ${msg}` }] }
          }
          return { content: [{ type: 'text', text: `Error: ${msg}` }] }
        }
      }
    )

    // gmail.createLabel — create a user label
    this.server.tool(
      "gmail.createLabel",
      "Create a Gmail label (requires gmail.modify; HITL confirm)",
      {
        accessToken: z.string(),
        name: z.string().min(1),
        color: z
          .object({
            textColor: z.string().optional(),
            backgroundColor: z.string().optional(),
          })
          .optional(),
        confirm: z.boolean().optional().default(false),
      },
      async ({ accessToken, name, color, confirm }) => {
        if (confirm !== true) {
          return confirmRequired('gmail.createLabel', { name, color }, 'This will create a new label. Please confirm.')
        }
        try {
          const body = {
            name,
            labelListVisibility: 'labelShow',
            messageListVisibility: 'show',
            ...(color ? { color } : {}),
          }
          const data = await gmailFetch<any>(accessToken, `users/me/labels`, {
            method: 'POST',
            headers: { 'content-type': 'application/json' },
            body: JSON.stringify(body),
          })
          try {
            const convId = `labels`
            if (this.env.DB) await upsertConversation(this.env.DB, convId, `Labels`, null)
            if (this.env.DB) await insertMessage(this.env.DB, {
              conversation_id: convId,
              role: 'tool',
              tool_name: 'gmail.createLabel',
              args_json: JSON.stringify({ name, color }),
              content: null,
              result_excerpt: JSON.stringify({ id: data.id, name: data.name }).slice(0, 1000),
            })
          } catch {}
          return { content: [{ type: 'text', text: JSON.stringify(data, null, 2) }] }
        } catch (e: any) {
          const msg = String(e?.message || e)
          if (msg.includes('403')) {
            return { content: [{ type: 'text', text: `ERR_INSUFFICIENT_SCOPE: gmail.modify required. Detail: ${msg}` }] }
          }
          return { content: [{ type: 'text', text: `Error: ${msg}` }] }
        }
      }
    )

    // gmail.deleteMessage — permanently delete (irreversible). Prefer trash if unsure.
    this.server.tool(
      "gmail.deleteMessage",
      "Permanently delete a Gmail message (irreversible). Prefer trash if unsure. Requires gmail.modify; HITL confirm.",
      {
        accessToken: z.string(),
        id: z.string().describe('Message ID'),
        confirm: z.boolean().optional().default(false),
      },
      async ({ accessToken, id, confirm }) => {
        if (confirm !== true) {
          return confirmRequired('gmail.deleteMessage', { id }, 'This will permanently delete the message. This cannot be undone. Please confirm.')
        }
        try {
          await gmailFetchNoContent(accessToken, `users/me/messages/${id}`, { method: 'DELETE' })
          try {
            const convId = `message:${id}`
            if (this.env.DB) await upsertConversation(this.env.DB, convId, `Message ${id}`, null)
            if (this.env.DB) await insertMessage(this.env.DB, {
              conversation_id: convId,
              role: 'tool',
              tool_name: 'gmail.deleteMessage',
              args_json: JSON.stringify({ id }),
              content: null,
              result_excerpt: JSON.stringify({ deleted: true }).slice(0, 1000),
            })
          } catch {}
          return { content: [{ type: 'text', text: JSON.stringify({ ok: true, deleted: true }, null, 2) }] }
        } catch (e: any) {
          const msg = String(e?.message || e)
          if (msg.includes('403')) {
            return { content: [{ type: 'text', text: `ERR_INSUFFICIENT_SCOPE: gmail.modify required. Detail: ${msg}` }] }
          }
          return { content: [{ type: 'text', text: `Error: ${msg}` }] }
        }
      }
    )

    // gmail.archive — remove INBOX (skip inbox)
    this.server.tool(
      "gmail.archive",
      "Archive a Gmail message by removing INBOX label (requires gmail.modify; HITL confirm)",
      {
        accessToken: z.string(),
        id: z.string().describe('Message ID'),
        confirm: z.boolean().optional().default(false),
      },
      async ({ accessToken, id, confirm }) => {
        if (confirm !== true) {
          return confirmRequired('gmail.archive', { id }, 'This will archive the message (remove from INBOX). Please confirm.')
        }
        try {
          const data = await gmailFetch<any>(accessToken, `users/me/messages/${id}/modify`, {
            method: 'POST',
            headers: { 'content-type': 'application/json' },
            body: JSON.stringify({ addLabelIds: [], removeLabelIds: [SYS.INBOX] }),
          })
          try {
            const convId = `message:${id}`
            if (this.env.DB) await upsertConversation(this.env.DB, convId, `Message ${id}`, null)
            if (this.env.DB) await insertMessage(this.env.DB, {
              conversation_id: convId,
              role: 'tool',
              tool_name: 'gmail.archive',
              args_json: JSON.stringify({ id }),
              content: null,
              result_excerpt: JSON.stringify({ labelIds: data.labelIds }).slice(0, 1000),
            })
          } catch {}
          return { content: [{ type: 'text', text: JSON.stringify(data, null, 2) }] }
        } catch (e: any) {
          const msg = String(e?.message || e)
          if (msg.includes('403')) {
            return { content: [{ type: 'text', text: `ERR_INSUFFICIENT_SCOPE: gmail.modify required. Detail: ${msg}` }] }
          }
          return { content: [{ type: 'text', text: `Error: ${msg}` }] }
        }
      }
    )

    // gmail.spam — add SPAM label
    this.server.tool(
      "gmail.spam",
      "Mark a Gmail message as spam by adding SPAM label (requires gmail.modify; HITL confirm)",
      {
        accessToken: z.string(),
        id: z.string().describe('Message ID'),
        confirm: z.boolean().optional().default(false),
      },
      async ({ accessToken, id, confirm }) => {
        if (confirm !== true) {
          return confirmRequired('gmail.spam', { id }, 'This will move the message to Spam (add SPAM label). Please confirm.')
        }
        try {
          const data = await gmailFetch<any>(accessToken, `users/me/messages/${id}/modify`, {
            method: 'POST',
            headers: { 'content-type': 'application/json' },
            body: JSON.stringify({ addLabelIds: [SYS.SPAM], removeLabelIds: [] }),
          })
          try {
            const convId = `message:${id}`
            if (this.env.DB) await upsertConversation(this.env.DB, convId, `Message ${id}`, null)
            if (this.env.DB) await insertMessage(this.env.DB, {
              conversation_id: convId,
              role: 'tool',
              tool_name: 'gmail.spam',
              args_json: JSON.stringify({ id }),
              content: null,
              result_excerpt: JSON.stringify({ labelIds: data.labelIds }).slice(0, 1000),
            })
          } catch {}
          return { content: [{ type: 'text', text: JSON.stringify(data, null, 2) }] }
        } catch (e: any) {
          const msg = String(e?.message || e)
          if (msg.includes('403')) {
            return { content: [{ type: 'text', text: `ERR_INSUFFICIENT_SCOPE: gmail.modify required. Detail: ${msg}` }] }
          }
          return { content: [{ type: 'text', text: `Error: ${msg}` }] }
        }
      }
    )

    // gmail.trash — move to TRASH (soft delete)
    this.server.tool(
      "gmail.trash",
      "Move a Gmail message to Trash (soft delete) (requires gmail.modify; HITL confirm)",
      {
        accessToken: z.string(),
        id: z.string().describe('Message ID'),
        confirm: z.boolean().optional().default(false),
      },
      async ({ accessToken, id, confirm }) => {
        if (confirm !== true) {
          return confirmRequired('gmail.trash', { id }, 'This will move the message to Trash. Please confirm.')
        }
        try {
          // Use dedicated trash endpoint (returns Message)
          const data = await gmailFetch<any>(accessToken, `users/me/messages/${id}/trash`, { method: 'POST' })
          try {
            const convId = `message:${id}`
            if (this.env.DB) await upsertConversation(this.env.DB, convId, `Message ${id}`, null)
            if (this.env.DB) await insertMessage(this.env.DB, {
              conversation_id: convId,
              role: 'tool',
              tool_name: 'gmail.trash',
              args_json: JSON.stringify({ id }),
              content: null,
              result_excerpt: JSON.stringify({ labelIds: data.labelIds }).slice(0, 1000),
            })
          } catch {}
          return { content: [{ type: 'text', text: JSON.stringify(data, null, 2) }] }
        } catch (e: any) {
          const msg = String(e?.message || e)
          if (msg.includes('403')) {
            return { content: [{ type: 'text', text: `ERR_INSUFFICIENT_SCOPE: gmail.modify required. Detail: ${msg}` }] }
          }
          return { content: [{ type: 'text', text: `Error: ${msg}` }] }
        }
      }
    )

    // ---------------------------
    // Phase 4.2 — Compose & Send (Chat-driven)
    // ---------------------------

    // Helper: preprocess recipient arrays
    const toArray = (v: unknown): string[] | undefined => {
      if (v == null) return undefined
      if (Array.isArray(v)) return v as string[]
      if (typeof v === 'string') return v.split(',').map((s) => s.trim()).filter(Boolean)
      return undefined
    }

    // Helper: base64url encode a UTF-8 string
    function base64UrlEncodeUtf8(input: string): string {
      const bytes = new TextEncoder().encode(input)
      let binary = ''
      for (let i = 0; i < bytes.length; i++) binary += String.fromCharCode(bytes[i])
      const b64 = btoa(binary)
      return b64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/g, '')
    }

    // Helper: chunk a long base64 string to 76-char lines (recommended for MIME readability)
    function chunkBase64(s: string): string {
      const out: string[] = []
      for (let i = 0; i < s.length; i += 76) out.push(s.slice(i, i + 76))
      return out.join('\r\n')
    }

    // Helper: build simple MIME message (text/plain + optional attachments)
    type AttachmentInput = { filename: string; mimeType: string; dataBase64: string }
    function buildMimeMessage(opts: {
      from?: string
      to: string[]
      cc?: string[]
      bcc?: string[]
      subject: string
      text: string
      attachments?: AttachmentInput[]
    }): string {
      const crlf = '\r\n'
      const headers: string[] = []
      if (opts.from) headers.push(`From: ${opts.from}`)
      headers.push(`To: ${opts.to.join(', ')}`)
      if (opts.cc?.length) headers.push(`Cc: ${opts.cc.join(', ')}`)
      if (opts.bcc?.length) headers.push(`Bcc: ${opts.bcc.join(', ')}`)
      headers.push(`Subject: ${opts.subject}`)
      headers.push('MIME-Version: 1.0')

      if (!opts.attachments || opts.attachments.length === 0) {
        headers.push('Content-Type: text/plain; charset=UTF-8')
        headers.push('Content-Transfer-Encoding: 7bit')
        const head = headers.join(crlf)
        const body = opts.text
        return head + crlf + crlf + body
      }

      const boundary = 'mixed_' + Math.random().toString(36).slice(2)
      headers.push(`Content-Type: multipart/mixed; boundary="${boundary}"`)
      const head = headers.join(crlf)

      let body = ''
      // Text part
      body += `--${boundary}` + crlf
      body += 'Content-Type: text/plain; charset=UTF-8' + crlf
      body += 'Content-Transfer-Encoding: 7bit' + crlf + crlf
      body += opts.text + crlf

      // Attachments
      for (const att of opts.attachments) {
        body += `--${boundary}` + crlf
        body += `Content-Type: ${att.mimeType}; name="${att.filename}"` + crlf
        body += 'Content-Transfer-Encoding: base64' + crlf
        body += `Content-Disposition: attachment; filename="${att.filename}"` + crlf + crlf
        body += chunkBase64(att.dataBase64) + crlf
      }
      body += `--${boundary}--` + crlf

      return head + crlf + crlf + body
    }

    const recipientsArray = z.preprocess((v) => toArray(v) ?? v, z.array(z.string()))
    const recipientsArrayOptional = z.preprocess((v) => (v == null ? undefined : (toArray(v) ?? v)), z.array(z.string()).optional())
    const attachmentsSchema = z.array(z.object({
      filename: z.string(),
      mimeType: z.string(),
      dataBase64: z.string(),
    })).optional()

    // gmail.createDraft — build MIME (or use raw) and create a draft
    this.server.tool(
      "gmail.createDraft",
      "Create a Gmail draft (requires gmail.compose). Supports simple text and basic attachments.",
      {
        accessToken: z.string(),
        // Either provide raw, or provide fields to build MIME
        raw: z.string().optional(),
        from: z.string().optional(),
        to: recipientsArray.describe('List of recipient emails (array or comma-separated string)'),
        cc: recipientsArrayOptional,
        bcc: recipientsArrayOptional,
        subject: z.string(),
        text: z.string(),
        attachments: attachmentsSchema,
      },
      async ({ accessToken, raw, from, to, cc, bcc, subject, text, attachments }) => {
        try {
          const encoded = raw ?? base64UrlEncodeUtf8(
            buildMimeMessage({ from, to, cc, bcc, subject, text, attachments })
          )
          const data = await gmailFetch<any>(accessToken, `users/me/drafts`, {
            method: 'POST',
            headers: { 'content-type': 'application/json' },
            body: JSON.stringify({ message: { raw: encoded } }),
          })
          try {
            const convId = `draft:create:${data.id || subject}`
            if (this.env.DB) await upsertConversation(this.env.DB, convId, `Draft Create: ${subject}`, null)
            if (this.env.DB) await insertMessage(this.env.DB, {
              conversation_id: convId,
              role: 'tool',
              tool_name: 'gmail.createDraft',
              args_json: JSON.stringify({ from, to, cc, bcc, subject, hasAttachments: !!attachments?.length, usedRaw: !!raw }),
              content: null,
              result_excerpt: JSON.stringify({ id: data.id, threadId: data.message?.threadId }).slice(0, 1000),
            })
          } catch {}
          return { content: [{ type: 'text', text: JSON.stringify(data, null, 2) }] }
        } catch (e: any) {
          const msg = String(e?.message || e)
          if (msg.includes('403')) {
            return { content: [{ type: 'text', text: `ERR_INSUFFICIENT_SCOPE: gmail.compose required. Detail: ${msg}` }] }
          }
          return { content: [{ type: 'text', text: `Error: ${msg}` }] }
        }
      }
    )

    // gmail.updateDraft — replace draft content with new MIME (or raw)
    this.server.tool(
      "gmail.updateDraft",
      "Update an existing Gmail draft (requires gmail.compose). Replaces the message content.",
      {
        accessToken: z.string(),
        draftId: z.string(),
        raw: z.string().optional(),
        from: z.string().optional(),
        to: recipientsArray,
        cc: recipientsArrayOptional,
        bcc: recipientsArrayOptional,
        subject: z.string(),
        text: z.string(),
        attachments: attachmentsSchema,
      },
      async ({ accessToken, draftId, raw, from, to, cc, bcc, subject, text, attachments }) => {
        try {
          const encoded = raw ?? base64UrlEncodeUtf8(
            buildMimeMessage({ from, to, cc, bcc, subject, text, attachments })
          )
          const data = await gmailFetch<any>(accessToken, `users/me/drafts/${draftId}`, {
            method: 'PUT',
            headers: { 'content-type': 'application/json' },
            body: JSON.stringify({ message: { raw: encoded } }),
          })
          try {
            const convId = `draft:${draftId}`
            if (this.env.DB) await upsertConversation(this.env.DB, convId, `Draft ${draftId}`, null)
            if (this.env.DB) await insertMessage(this.env.DB, {
              conversation_id: convId,
              role: 'tool',
              tool_name: 'gmail.updateDraft',
              args_json: JSON.stringify({ draftId, from, to, cc, bcc, subject, hasAttachments: !!attachments?.length, usedRaw: !!raw }),
              content: null,
              result_excerpt: JSON.stringify({ id: data.id, threadId: data.message?.threadId }).slice(0, 1000),
            })
          } catch {}
          return { content: [{ type: 'text', text: JSON.stringify(data, null, 2) }] }
        } catch (e: any) {
          const msg = String(e?.message || e)
          if (msg.includes('403')) {
            return { content: [{ type: 'text', text: `ERR_INSUFFICIENT_SCOPE: gmail.compose required. Detail: ${msg}` }] }
          }
          return { content: [{ type: 'text', text: `Error: ${msg}` }] }
        }
      }
    )

    // gmail.sendDraft — send an existing draft, optionally updating its content first
    this.server.tool(
      "gmail.sendDraft",
      "Send an existing Gmail draft (requires gmail.send). Optional content update via raw or fields. HITL confirm required.",
      {
        accessToken: z.string(),
        draftId: z.string(),
        confirm: z.boolean().optional().default(false),
        raw: z.string().optional(),
        from: z.string().optional(),
        to: recipientsArrayOptional,
        cc: recipientsArrayOptional,
        bcc: recipientsArrayOptional,
        subject: z.string().optional(),
        text: z.string().optional(),
        attachments: attachmentsSchema,
      },
      async ({ accessToken, draftId, confirm, raw, from, to, cc, bcc, subject, text, attachments }) => {
        if (confirm !== true) {
          return confirmRequired('gmail.sendDraft', { draftId, hasUpdate: !!(raw || to || cc || bcc || subject || text || attachments?.length) }, 'This will send the draft email. Please confirm to proceed.')
        }
        try {
          let body: any = { id: draftId }
          if (raw || to || cc || bcc || subject || text || (attachments && attachments.length)) {
            const encoded = raw ?? base64UrlEncodeUtf8(
              buildMimeMessage({
                from,
                to: to || [],
                cc: cc || [],
                bcc: bcc || [],
                subject: subject || '(no subject)',
                text: text || '',
                attachments,
              })
            )
            body.message = { raw: encoded }
          }
          const data = await gmailFetch<any>(accessToken, `users/me/drafts/send`, {
            method: 'POST',
            headers: { 'content-type': 'application/json' },
            body: JSON.stringify(body),
          })
          try {
            const convId = `draft:${draftId}`
            if (this.env.DB) await upsertConversation(this.env.DB, convId, `Draft ${draftId}`, null)
            if (this.env.DB) await insertMessage(this.env.DB, {
              conversation_id: convId,
              role: 'tool',
              tool_name: 'gmail.sendDraft',
              args_json: JSON.stringify({ draftId, updated: !!body.message }),
              content: null,
              result_excerpt: JSON.stringify({ sentMessageId: data.id, threadId: data.threadId }).slice(0, 1000),
            })
          } catch {}
          return { content: [{ type: 'text', text: JSON.stringify(data, null, 2) }] }
        } catch (e: any) {
          const msg = String(e?.message || e)
          if (msg.includes('403')) {
            return { content: [{ type: 'text', text: `ERR_INSUFFICIENT_SCOPE: gmail.send required. Detail: ${msg}` }] }
          }
          return { content: [{ type: 'text', text: `Error: ${msg}` }] }
        }
      }
    )

    // gmail.sendMessage — send a new message immediately
    this.server.tool(
      "gmail.sendMessage",
      "Send a new Gmail message (requires gmail.send). Supports simple text and basic attachments. HITL confirm required.",
      {
        accessToken: z.string(),
        confirm: z.boolean().optional().default(false),
        raw: z.string().optional(),
        from: z.string().optional(),
        to: recipientsArray,
        cc: recipientsArrayOptional,
        bcc: recipientsArrayOptional,
        subject: z.string(),
        text: z.string(),
        attachments: attachmentsSchema,
      },
      async ({ accessToken, confirm, raw, from, to, cc, bcc, subject, text, attachments }) => {
        if (confirm !== true) {
          return confirmRequired('gmail.sendMessage', { to, cc, bcc, subject, hasAttachments: !!attachments?.length }, 'This will send an email to the specified recipients. Please confirm to proceed.')
        }
        try {
          const encoded = raw ?? base64UrlEncodeUtf8(
            buildMimeMessage({ from, to, cc, bcc, subject, text, attachments })
          )
          const data = await gmailFetch<any>(accessToken, `users/me/messages/send`, {
            method: 'POST',
            headers: { 'content-type': 'application/json' },
            body: JSON.stringify({ raw: encoded }),
          })
          try {
            const convId = `message:sent:${data.id || subject}`
            if (this.env.DB) await upsertConversation(this.env.DB, convId, `Message Sent: ${subject}`, null)
            if (this.env.DB) await insertMessage(this.env.DB, {
              conversation_id: convId,
              role: 'tool',
              tool_name: 'gmail.sendMessage',
              args_json: JSON.stringify({ to, cc, bcc, subject, hasAttachments: !!attachments?.length, usedRaw: !!raw }),
              content: null,
              result_excerpt: JSON.stringify({ id: data.id, threadId: data.threadId }).slice(0, 1000),
            })
          } catch {}
          return { content: [{ type: 'text', text: JSON.stringify(data, null, 2) }] }
        } catch (e: any) {
          const msg = String(e?.message || e)
          if (msg.includes('403')) {
            return { content: [{ type: 'text', text: `ERR_INSUFFICIENT_SCOPE: gmail.send required. Detail: ${msg}` }] }
          }
          return { content: [{ type: 'text', text: `Error: ${msg}` }] }
        }
      }
    )
  }
}
