import { DEFAULT_SCOPES, BACKEND_BASE_URL, OAUTH_ACCESS_TYPE, OAUTH_INCLUDE_GRANTED_SCOPES, OAUTH_PROMPT } from '../lib/config'
import { generatePKCE, randomState } from './pkce'
import type { AccountsState, Account } from '../types'

const STORAGE_KEY = 'ai_email_agent_accounts_state'

async function getState(): Promise<AccountsState> {
  const data = await chrome.storage.local.get(STORAGE_KEY)
  return (data[STORAGE_KEY] as AccountsState) || { accounts: [] }
}

async function setState(s: AccountsState) {
  await chrome.storage.local.set({ [STORAGE_KEY]: s })
}

function computeExpiry(expiresInSec: number) {
  return Date.now() + expiresInSec * 1000 - 60_000 // minus 60s buffer
}

async function getAuthorizeUrl(clientId: string, codeChallenge: string, redirectUri: string, scopes: string[], state?: string) {
  const params = new URLSearchParams({
    client_id: clientId,
    response_type: 'code',
    redirect_uri: redirectUri,
    scope: scopes.join(' '),
    access_type: OAUTH_ACCESS_TYPE,
    include_granted_scopes: OAUTH_INCLUDE_GRANTED_SCOPES,
    prompt: OAUTH_PROMPT,
    code_challenge: codeChallenge,
    code_challenge_method: 'S256',
  })
  if (state) params.set('state', state)
  return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`
}

async function fetchClientId(): Promise<string> {
  try {
    const res = await fetch(`${BACKEND_BASE_URL}/oauth/google/config`)
    if (!res.ok) throw new Error(`config ${res.status}`)
    const json = await res.json()
    if (!json.client_id) throw new Error('missing client_id')
    return json.client_id as string
  } catch (e) {
    console.warn('[Auth] fallback to VITE_GOOGLE_CLIENT_ID', e)
    const cid = (import.meta as any).env?.VITE_GOOGLE_CLIENT_ID
    if (!cid) throw new Error('No client_id available')
    return cid
  }
}

export async function addAccount() {
  const { verifier, challenge } = await generatePKCE()
  const redirectUri = chrome.identity.getRedirectURL()
  const state = randomState(16)
  const clientId = await fetchClientId()
  const url = await getAuthorizeUrl(clientId, challenge, redirectUri, DEFAULT_SCOPES, state)

  // Persist state & verifier temporarily for safety in case SW is suspended
  await chrome.storage.session.set({ oauth_state: state, oauth_verifier: verifier, oauth_redirect: redirectUri })

  const redirect = await chrome.identity.launchWebAuthFlow({ url, interactive: true })
  const u = new URL(redirect)
  const code = u.searchParams.get('code')
  const returnedState = u.searchParams.get('state')
  const error = u.searchParams.get('error')
  if (error) throw new Error(`OAuth error: ${error}`)
  if (!code) throw new Error('No authorization code')
  const { oauth_state } = await chrome.storage.session.get('oauth_state')
  if (!returnedState || oauth_state !== returnedState) {
    throw new Error('State mismatch')
  }

  const tokenRes = await fetch(`${BACKEND_BASE_URL}/oauth/google/token`, {
    method: 'POST',
    headers: { 'content-type': 'application/json' },
    body: JSON.stringify({ code, code_verifier: verifier, redirect_uri: redirectUri }),
  })
  if (!tokenRes.ok) throw new Error(`token exchange failed ${tokenRes.status}`)
  const tokenJson = await tokenRes.json()
  const { access_token, expires_in, refresh_token, id_token, user } = tokenJson

  const acct: Account = {
    id: user?.sub || user?.email || crypto.randomUUID(),
    email: user?.email || 'unknown',
    picture: user?.picture,
    tokensRef: refresh_token || '',
    accessToken: access_token,
    accessTokenExp: computeExpiry(expires_in || 3600),
  }

  const s = await getState()
  const exists = s.accounts.find((a) => a.email === acct.email)
  if (exists) {
    Object.assign(exists, acct)
    s.activeAccountId = exists.id
  } else {
    s.accounts.push(acct)
    s.activeAccountId = acct.id
  }
  await setState(s)
  await chrome.storage.session.remove(['oauth_state', 'oauth_verifier', 'oauth_redirect'])
  return acct
}

export async function listAccounts() {
  const s = await getState()
  return s
}

export async function switchAccount(accountId: string) {
  const s = await getState()
  if (s.accounts.some((a) => a.id === accountId)) {
    s.activeAccountId = accountId
    await setState(s)
    return true
  }
  return false
}

export async function signoutAccount(accountId: string) {
  const s = await getState()
  const idx = s.accounts.findIndex((a) => a.id === accountId)
  if (idx >= 0) {
    const removed = s.accounts.splice(idx, 1)[0]
    if (s.activeAccountId === removed.id) s.activeAccountId = s.accounts[0]?.id
    await setState(s)
    // optional revoke
    try {
      if (removed.accessToken) {
        await fetch(`https://oauth2.googleapis.com/revoke?token=${encodeURIComponent(removed.accessToken)}`, { method: 'POST', headers: { 'content-type': 'application/x-www-form-urlencoded' } })
      }
    } catch {}
    return true
  }
  return false
}

export async function getActiveAccessToken() {
  const s = await getState()
  const acct = s.accounts.find((a) => a.id === s.activeAccountId)
  if (!acct) throw new Error('No active account')
  if (acct.accessToken && acct.accessTokenExp && Date.now() < acct.accessTokenExp) return { access_token: acct.accessToken }
  if (!acct.tokensRef) throw new Error('No refresh token')
  const res = await fetch(`${BACKEND_BASE_URL}/oauth/google/refresh`, {
    method: 'POST',
    headers: { 'content-type': 'application/json' },
    body: JSON.stringify({ refresh_token: acct.tokensRef }),
  })
  if (!res.ok) throw new Error(`refresh failed ${res.status}`)
  const json = await res.json()
  acct.accessToken = json.access_token
  acct.accessTokenExp = computeExpiry(json.expires_in || 3600)
  await setState(s)
  return { access_token: acct.accessToken }
}

export type BgMessage =
  | { type: 'accounts/list' }
  | { type: 'accounts/add' }
  | { type: 'accounts/switch'; accountId: string }
  | { type: 'accounts/signout'; accountId: string }
  | { type: 'tokens/getActive' }

export async function handleMessage(msg: BgMessage) {
  switch (msg.type) {
    case 'accounts/list':
      return await listAccounts()
    case 'accounts/add':
      return await addAccount()
    case 'accounts/switch':
      return await switchAccount(msg.accountId)
    case 'accounts/signout':
      return await signoutAccount(msg.accountId)
    case 'tokens/getActive':
      return await getActiveAccessToken()
    default:
      return undefined
  }
}
