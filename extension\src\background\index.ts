import { handleMessage, type BgMessage } from './accounts'

chrome.runtime.onInstalled.addListener(() => {
  console.log('[AI Email Agent] Extension installed')
  // Setup Side Panel default path and behavior (Chrome 127+)
  if ('sidePanel' in chrome && chrome.sidePanel) {
    try {
      // Ensure the side panel is enabled and points to our entry
      chrome.sidePanel.setOptions({ path: 'index.html', enabled: true })
      // Open the side panel when the user clicks the extension action icon
      // (If not supported in the current Chrome version, optional chaining prevents errors)
      // @ts-expect-error: setPanelBehavior may be missing in older type definitions
      chrome.sidePanel.setPanelBehavior?.({ openPanelOnActionClick: true })
    } catch (err) {
      console.warn('[AI Email Agent] sidePanel setup skipped:', err)
    }
  }
})

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message?.type === 'PING') {
    sendResponse({ ok: true, ts: Date.now() })
    return true
  }
  // Phase 1b message bus
  if (message?.type && (typeof message.type === 'string') && (
    message.type.startsWith('accounts/') || message.type === 'tokens/getActive'
  )) {
    Promise.resolve(handleMessage(message as BgMessage))
      .then((res) => sendResponse(res))
      .catch((err) => sendResponse({ error: String(err?.message || err) }))
    return true // keep channel open for async
  }
})
