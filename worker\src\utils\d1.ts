import type { D1Database } from 'cloudflare:workers'

// Conversations and messages schema for transcripts persistence
// - conversations: minimal metadata per conversation
// - messages: each user/assistant/tool message with optional tool args/result excerpts

export async function ensureSchema(db: D1Database) {
  // Create tables if not exist
  await db.batch([
    db.prepare(`
      CREATE TABLE IF NOT EXISTS conversations (
        id TEXT PRIMARY KEY,
        title TEXT,
        account_id TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      );
    `),
    db.prepare(`
      CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        conversation_id TEXT NOT NULL,
        role TEXT NOT NULL, -- user | assistant | tool
        tool_name TEXT,
        args_json TEXT,
        content TEXT,
        result_excerpt TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY(conversation_id) REFERENCES conversations(id)
      );
    `),
    db.prepare(`CREATE INDEX IF NOT EXISTS idx_messages_conv_created ON messages(conversation_id, created_at);`),
    db.prepare(`CREATE INDEX IF NOT EXISTS idx_conversations_updated ON conversations(updated_at DESC);`),
  ])
}

export async function upsertConversation(
  db: D1Database,
  id: string,
  title: string | null,
  accountId: string | null,
) {
  const now = Date.now()
  // Try insert; on conflict(id) update title/updated_at
  await db.prepare(
    `INSERT INTO conversations (id, title, account_id, created_at, updated_at)
     VALUES (?1, ?2, ?3, ?4, ?4)
     ON CONFLICT(id) DO UPDATE SET
       title=COALESCE(excluded.title, conversations.title),
       account_id=COALESCE(excluded.account_id, conversations.account_id),
       updated_at=excluded.updated_at`
  ).bind(id, title, accountId, now).run()
}

export type InsertMessageInput = {
  conversation_id: string
  role: 'user' | 'assistant' | 'tool'
  tool_name?: string | null
  args_json?: string | null
  content?: string | null
  result_excerpt?: string | null
}

export async function insertMessage(db: D1Database, msg: InsertMessageInput) {
  const now = Date.now()
  await db.prepare(
    `INSERT INTO messages (conversation_id, role, tool_name, args_json, content, result_excerpt, created_at)
     VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)`
  )
    .bind(
      msg.conversation_id,
      msg.role,
      msg.tool_name ?? null,
      msg.args_json ?? null,
      msg.content ?? null,
      msg.result_excerpt ?? null,
      now,
    )
    .run()

  // Touch conversation.updated_at
  await db.prepare(`UPDATE conversations SET updated_at=?2 WHERE id=?1`).bind(msg.conversation_id, now).run()
}

// --- Phase 5 helpers: list conversations and messages ---

export type ConversationRow = {
  id: string
  title: string | null
  account_id: string | null
  created_at: number
  updated_at: number
}

export async function listConversations(db: D1Database, limit = 100): Promise<ConversationRow[]> {
  const stmt = db.prepare(
    `SELECT id, title, account_id, created_at, updated_at
     FROM conversations
     ORDER BY updated_at DESC
     LIMIT ?1`
  ).bind(limit)
  const res = await stmt.all<ConversationRow>()
  return res.results || []
}

export type MessageRow = {
  id: number
  conversation_id: string
  role: 'user' | 'assistant' | 'tool'
  tool_name: string | null
  args_json: string | null
  content: string | null
  result_excerpt: string | null
  created_at: number
}

export async function listMessages(db: D1Database, conversationId: string, limit = 500): Promise<MessageRow[]> {
  const stmt = db.prepare(
    `SELECT id, conversation_id, role, tool_name, args_json, content, result_excerpt, created_at
     FROM messages
     WHERE conversation_id = ?1
     ORDER BY created_at ASC
     LIMIT ?2`
  ).bind(conversationId, limit)
  const res = await stmt.all<MessageRow>()
  return res.results || []
}

// Helper to insert a plain user message (Phase 5 chat input)
export async function insertUserMessage(db: D1Database, conversationId: string, content: string) {
  await insertMessage(db, {
    conversation_id: conversationId,
    role: 'user',
    content,
  })
}
