# AI Email Agent Chrome Extension (MV3) — Task Plan

This task plan follows `prd.md`. Target year: 2025. Package manager: Bun. Code language: English (FE + BE). Docs language: Vietnamese.

## 1) Repo Structure
Monorepo (two packages):
- `extension/` — React + Vite + `@crxjs/vite-plugin`, Tailwind v4 + shadcn/ui, MV3 side panel.
- `worker/` — Cloudflare Workers + Agents SDK (MCP), Hono router, OAuth token exchange, Gmail tool proxy, AI Gateway calls.

Suggested tree:
```
root/
  extension/
    src/
      background/
      sidepanel/
      content/
      lib/
    manifest.json
    index.html (side panel)
    vite.config.ts
    tailwind.config.ts
    components.json (shadcn)
  worker/
    src/
      index.ts (Hono + Agents)
      mcp/
      gmail/
      utils/
    wrangler.toml
  docs/
    prd.md
    task.md
```

## 2) Environments & Secrets
Prerequisites:
- Bun latest, Chrome 127+, Cloudflare account + Wrangler, Google Cloud project with OAuth (Web App), Gmail API & People API enabled.
- Cloudflare AI Gateway created (Universal Endpoint) and routed to Google Gemini 2.5 Flash Lite.

Worker secrets (Wrangler):
- `GOOGLE_CLIENT_ID`
- `GOOGLE_CLIENT_SECRET`
- `OAUTH_REDIRECT_URI` (point to Worker route)
- `SESSION_SECRET` (sign JWT/state)
- `AI_GATEWAY_URL` (Universal Endpoint URL)
- `AI_GATEWAY_TOKEN` (if required by Gateway)
- `ENCRYPTION_KEY` (optional, for encrypting refresh tokens in Durable Object)

Extension env (via `import.meta.env` + build-time define):
- `VITE_BACKEND_BASE_URL`
- `VITE_USE_BACKEND_TOKEN_EXCHANGE=true`

## 3) Phase Breakdown & Tasks

### Phase 1 — Scaffolding & Build System
- Initialize `extension/` with React + Vite + `@crxjs/vite-plugin` (MV3 side_panel) using Bun.
- Add Tailwind v4, configure PostCSS, import base styles.
- Initialize shadcn/ui (`components.json`), add core primitives: Button, Input, Dialog, DropdownMenu, ScrollArea, Avatar, Skeleton.
- Create `manifest.json` (MV3): `side_panel`, `background.service_worker`, `host_permissions` (backend), `permissions` (identity, storage), `oauth2` section not used (we use `launchWebAuthFlow`).
- `background/` bootstrap: message bus, identity checks, error boundary.
- Dev scripts & build scripts with Bun.

Acceptance:
- `bun run dev` starts extension HMR with CRXJS; side panel renders a Hello UI.

### Phase 1b — OAuth Multi-account (PKCE + WebAuthFlow)
- Implement `chrome.identity.launchWebAuthFlow` with PKCE.
- Backend Worker endpoints: `/oauth/google/token` (code->tokens), `/oauth/google/refresh`.
- Store per-account tokens (access + refresh reference) in `chrome.storage.session` or `local`; store refresh server-side (Durable Object) encrypted.
- Runtime messaging channels:
  - `accounts/list`, `accounts/add`, `accounts/switch`, `accounts/signout`.
  - `tokens/getActive` returns active access token (freshened if needed).
- UI: Account menu (avatar, email), add/switch/sign out.

 Acceptance:
 - Add multiple Google accounts; switch active; refresh & revoke work.

### Backlog — Manual UI (deferred)
- Inbox list with virtualization, filters, pagination, friendly time, clamp subject/snippet.
- Compose sheet UI + attachments preview/download.
- People API avatar lookup in list view.

### Phase 4.0 — Agent-first MVP (Agents + MCP, Read-only)
- Worker: integrate Agents SDK; expose MCP server at `/mcp` (streamable-http).
- Tools (read-only, đợt 1): `gmail.listThreads`, `gmail.getThread`, `gmail.getMessage`, `gmail.getAttachment`, `summarize.thread`.
- DO for per-user agent state/context; KV/D1 for transcripts.
- AI Gateway → Gemini 2.5 Flash Lite; streaming responses.
- Human-in-the-loop plumbing sẵn sàng (elicitInput + UI confirm), chưa bật hành động phá huỷ.

Acceptance:
- Chat gọi được tools read-only và stream kết quả; transcripts được lưu; elicit pipeline khả dụng.

### Phase 4.1 — Action Tools + HITL
- Bật tools thay đổi trạng thái: `gmail.modifyLabels`, `gmail.markReadUnread`, `gmail.createLabel` với confirm.
- Incremental scopes: xin `gmail.modify` khi lần đầu cần.
- Thêm tool `gmail.deleteMessage` với confirm.
- Thêm tool `gmail.archive` với confirm.
- Thêm tool `gmail.spam` với confirm.
- Thêm tool `gmail.trash` với confirm.


Acceptance:
- Thao tác mark read/unread/label qua Chat, qua confirm, thực thi thành công; tool traces rõ ràng.

### Phase 4.2 — Compose & Send (Chat-driven)
- MIME builder (server) + `createDraft`, `updateDraft`, `sendDraft`, `sendMessage`.
- Incremental scopes: xin `gmail.compose`, `gmail.send` khi lần đầu cần.
- Human-in-the-loop: luôn confirm trước gửi/xóa.

Acceptance:
- Agent tạo/update/send draft qua Chat sau khi confirm; attachments cơ bản hoạt động.

### Phase 4.3 — Scheduling (Cloudflare Workflows)
- `schedule.send` orchestration bằng Workflows; DO vẫn giữ state/actor.

Acceptance:
- Tạo draft ngay và lên lịch gửi; đến giờ và/hoặc sau confirm → gửi thành công.

### Phase 5 — Chat UI & Conversation Management
- Side panel chat similar to chatgpt.com: history on left, messages center, composer bottom.
- Show tool call entries, partial streaming, retry, copy.
- Conversation persistence per account; quick actions (Summarize inbox / Draft reply / Label thread).

Acceptance:
- Smooth chat UX; streaming; conversation saved & reloaded; tool call traces visible.

### Phase 6 — Security, Telemetry, Perf
- Scope minimization + incremental consent.
- Token lifecycle: rotation, revoke; zero-trust between FE and BE via short-lived access.
- CORS for extension origin; Gateway logs on.
- Error reporting and structured logs; rate-limit & debounce AI calls; cache summaries where safe.

Acceptance:
- No PII leakage to logs; destructive actions always confirmed; perf under target (render <100ms frame budget in list scroll).

### Phase 7 — Packaging & Release
- Production builds (extension zip, Worker deploy via Wrangler).
- Docs: onboarding, permissions, privacy, changelog.
- Internal dogfooding; staged rollout.

Acceptance:
- Extension loads cleanly; Worker stable; end-to-end flows pass.

## 4) Detailed Checklists

### Extension
- [ ] Vite + CRXJS + Bun scripts
- [ ] Tailwind v4 configured
- [ ] shadcn/ui installed; base components added
- [ ] MV3 `manifest.json` with side_panel + background
- [ ] Background service worker boot + messaging
- [ ] Account menu UI
- [ ] Mail list virtualization + filters + pagination (Backlog)
- [ ] People API avatar integration (Backlog)
- [ ] Compose sheet + attachments (Backlog)
- [ ] Chat UI (streaming, tool traces, confirmations)

### Worker
- [ ] Wrangler project + routes
- [ ] `/oauth/google/token`, `/oauth/google/refresh` implemented (PKCE exchange)
- [ ] Gmail helpers (read/modify/send/labels/attachments)
- [ ] Agents SDK + MCP at `/mcp`
- [ ] DO state for memory; KV/D1 transcripts
- [ ] AI Gateway client → Gemini 2.5 Flash Lite
- [ ] Schedule via Cloudflare Workflows for delayed send (orchestration; DO still used for state/actor)
- [ ] Human-in-the-loop elicit + policies

### Security & Compliance
- [ ] Incremental scopes & re-consent flows
- [ ] Encrypt refresh tokens at rest (server)
- [ ] CORS lock to extension
- [ ] Gateway logging + redaction
- [ ] Permissions review in `manifest.json`

### QA & Testing
- [ ] Unit tests: MIME builder (Phase 4.2+), label utils, time formatting
- [ ] Integration: OAuth flow, token refresh, MCP `/mcp` tool calls, AI Gateway streaming
- [ ] E2E manual: multi-account, action tools via Chat (confirm required), compose+send (4.2+), schedule (4.3+), delete needs confirm
- [ ] Performance: streaming latency, MCP call latency, DO/Workflows stability

## 5) Commands (Bun)

Extension:
- Install deps: `bun install`
- Dev: `bun run dev`
- Build: `bun run build`

Worker:
- Install deps: `bun install`
- Dev: `bunx wrangler dev`
- Deploy: `bunx wrangler deploy`
- Secrets: `bunx wrangler secret put <NAME>`

## 6) API Scopes (progressive)
- Base: `openid`, `email`, `profile`, `https://www.googleapis.com/auth/userinfo.email`
- Read: `https://www.googleapis.com/auth/gmail.readonly`
- Modify: `https://www.googleapis.com/auth/gmail.modify`
- Compose/Send: `https://www.googleapis.com/auth/gmail.compose`, `https://www.googleapis.com/auth/gmail.send`
- People (optional avatars): `https://www.googleapis.com/auth/contacts.readonly`

## 7) Test Plan (Key Cases)
- Phase 1/1b: Add/Switch/Sign out accounts; token refresh/revoke.
- Phase 4.0: Chat can call read-only tools; summarize long thread; stream stability; transcripts persisted.
- Phase 4.1: Read/unread toggle idempotent; labels apply/remove with confirm; policy gate enforced.
- Phase 4.2: Create/Update/Send draft (3+ attachments); sending without confirm blocked; with confirm proceeds.
- Phase 4.3: Schedule send (create draft now, send at T+5min via Workflows); verify sent; retries/observability.
- Backlog (Manual UI): List inbox 5k+ threads smoothly; filter correctness; People API avatars in list; compose UI interactions.

## 8) Timeline (suggested)
- Week 1: Phase 1 + 1b (auth)
- Week 2: Phase 4.0 (Agent-first MVP, read-only)
- Week 3: Phase 4.1 (Action tools + HITL)
- Week 4: Phase 4.2 (Compose/Send) + Phase 4.3 (Scheduling)
- Week 5: Phase 5 (chat UX) + Phase 6/7 (hardening, release)

## 9) Definition of Done
- All acceptance criteria per phase green.
- Security review passed; secrets managed; scopes minimal.
- E2E scenarios verified across 2+ Google accounts.
- Docs updated: setup, privacy, changelog.

## 10) References
- Cloudflare Agents + MCP: https://github.com/cloudflare/agents
- AI Gateway: https://developers.cloudflare.com/ai-gateway/
- CRXJS Vite plugin: https://github.com/crxjs/chrome-extension-tools
- shadcn/ui: https://ui.shadcn.com/
- Gmail API: https://developers.google.com/workspace/gmail/api
- Gemini API: https://ai.google.dev/gemini-api
