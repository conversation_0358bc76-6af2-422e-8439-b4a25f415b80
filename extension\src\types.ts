export type Account = {
  id: string
  email: string
  picture?: string
  tokensRef: string
  accessToken?: string
  accessTokenExp?: number // epoch ms
}

export type AccountsState = {
  accounts: Account[]
  activeAccountId?: string
}

// Phase 5: Conversation & Messages (mirrors worker D1 schema)
export type Conversation = {
  id: string
  title: string | null
  account_id: string | null
  created_at: number
  updated_at: number
}

export type ChatMessage = {
  id: number
  conversation_id: string
  role: 'user' | 'assistant' | 'tool'
  tool_name?: string | null
  args_json?: string | null
  content?: string | null
  result_excerpt?: string | null
  created_at: number
}
