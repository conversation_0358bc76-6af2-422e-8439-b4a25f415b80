# AI Email Agent Chrome Extension (MV3) — PRD

## 1) Summary
AI agent dạng Chrome Extension (sidebar) giúp quản lý Gmail đa tài khoản: đọc/tóm tắt/đ<PERSON>h kèm, xóa/gửi/schedule, phân loại/tạo label, lưu ngữ cảnh hội thoại, Human-in-the-Loop để phê duyệt hành động rủi ro. Backend Cloudflare Agents (hỗ trợ MCP), gọi model qua Cloudflare AI Gateway Universal Endpoint. Model mặc định: Google Gemini 2.5 Flash Lite.

Tech chính:
- Frontend: React + Vite + @crxjs/vite-plugin (MV3), Tailwind CSS v4, shadcn/ui. Quản lý gói: Bun.
- OAuth Gmail đa tài khoản: chrome.identity.launchWebAuthFlow + PKCE. Trao đổi token qua Cloudflare Worker (server-side token exchange) để bảo mật client_secret.
- Backend: Cloudflare Workers + Agents SDK (McpAgent), Cloudflare Workflows cho orchestration/schedule; Durable Objects cho state/memory (actor); KV/D1 cho lưu vết.
- AI: Cloudflare AI Gateway Universal Endpoint → Google Gemini 2.5 Flash Lite.

## 2) Goals
- Quản lý Gmail đa tài khoản trong sidebar UI thân thiện như chatgpt.com.
- Agent có MCP tools thao tác hộp thư: đọc mail/đính kèm, xóa, gửi, tạo draft, update draft, phân loại, gán/tạo label, tóm tắt, tổng hợp.
- Lưu được conversation context dài hạn, dùng để cải thiện câu trả lời và gợi ý.
- Human-in-the-loop: yêu cầu xác nhận trước khi gửi email/xóa/thao tác phá hủy.
- Hỗ trợ schedule send.

## 3) Non-goals
- Không thay thế hoàn toàn Gmail UI; không chỉnh sửa nội dung ngoài sandbox extension.
- Không triển khai E2E encryption custom (tuân thủ OAuth, HTTPS, redaction log qua Gateway).
- Không hỗ trợ provider email khác giai đoạn đầu (chỉ Gmail).

## 4) Personas & Key User Stories
- Power email user: “Mình muốn nhờ AI lọc, tóm tắt và trả lời nhanh nhiều email.”
- PM/Sales: “Tạo bản nháp, xin xác nhận rồi gửi; có thể schedule gửi.”
- Support: “Phân loại/ghi nhãn tự động, lôi file đính kèm liên quan.”

User stories tiêu biểu:
- Đăng nhập nhiều tài khoản, chuyển nhanh tài khoản hoạt động.
- Chat với AI để: tóm tắt thread hiện tại/inbox, trích xuất checklist, soạn phản hồi.
- Ra lệnh: “Gửi mail này cho A, CC B, đính kèm file C, 9h sáng mai.”
- Duyệt: Popup xác nhận trước khi gửi/xóa, sửa nội dung draft trước khi gửi.

## 5) Scope & Feature List
- MV3 sidebar + chat UI (shadcn/ui, Tailwind v4), streaming output.
- OAuth đa tài khoản (PKCE) + refresh token, lưu per-account (chrome.storage) và/hoặc DO phía server (mã hóa).
- Gmail tools (MCP):
  - Read: threads.list, threads.get (headers, snippet, attachments meta), messages.get, attachments.get.
  - Send: createDraft, updateDraft, sendDraft, sendMessage (MIME builder, attachments).
  - Labels: list, create, modify (gán/ bỏ nhãn), spam/trash nếu được phép.
  - Modify: mark read/unread, archive.
  - Summarize/categorize: dùng model để tổng hợp, gợi ý nhãn.
  - Schedule: orchestration bằng Cloudflare Workflows (tạo draft, chờ đến runAt, xin phê duyệt, gửi đúng giờ); DO vẫn dùng cho state/actor nếu cần.
- Context & Memory: Durable Objects lưu state agent, D1/KV lưu hội thoại và chỉ mục.
- Human-in-the-loop: 
  - Elicit input (Cloudflare Agents) để yêu cầu xác nhận, hoặc confirm modal trong UI.
  - Chính sách: mọi hành động phá hủy/gửi ra ngoài cần xác nhận (policy gate).
- AI Gateway: dùng Universal Endpoint (có log, cache, rate-limit); cấu hình model “gemini-2.5-flash-lite”.

## 6) Architecture
Thành phần:
- Chrome Extension (MV3)
  - Background Service Worker: OAuth PKCE + webAuthFlow, runtime messaging, token rotation, bridge tới backend.
  - Side Panel UI: chat, account switcher, thread context, danh sách đề xuất hành động, dialogs xác nhận.
  - Content script (tuỳ chọn): lấy context trang Gmail khi cần (subject/thread-id).
- Cloudflare Workers (Agents)
  - Hono/Router + Agents SDK (McpAgent). 
  - MCP endpoints: /mcp (streamable-http). 
  - OAuth token exchange/refresh: /oauth/google/token, /oauth/google/refresh.
  - Gmail proxy tools: gọi Gmail API bằng access token user (không lưu access token lâu; refresh quản lý an toàn).
  - Memory: Durable Objects (state per user/agent), D1/KV cho transcripts; orchestration schedule qua Cloudflare Workflows (thay vì Alarms).
  - AI: gọi AI Gateway (universal endpoint) → Gemini 2.5 Flash Lite.
- Cloudflare AI Gateway: logging, caching, routing, key mgmt.

Dòng chảy chính:
1) Extension khởi tạo → user add account → webAuthFlow (PKCE) → backend exchange token → nhận access/refresh.
2) User chat → UI gửi message + context (active account, thread) → backend agent → gọi MCP tools (nếu cần) → AI suy luận → trả kết quả stream về UI.
3) Hành động rủi ro → yêu cầu xác nhận (elicitInput + UI). Nếu accept → tool thực thi (Gmail API).
4) Schedule → khởi tạo Cloudflare Workflow run chờ tới thời điểm gửi và/hoặc tín hiệu phê duyệt → agent gửi draft.

## 7) Security & Privacy
- Token security:
  - Server-side token exchange để tránh lộ client_secret.
  - Refresh token: tuỳ cấu hình, ưu tiên lưu trên server (DO) được mã hoá bằng Secrets; access token ngắn hạn truyền theo yêu cầu.
- Least privilege scopes, chỉ yêu cầu khi cần: 
  - gmail.readonly, gmail.modify, gmail.compose, gmail.send, contacts.readonly (avatar People API), openid/userinfo.
- CORS lock-down giữa extension và backend; pin host_permissions.
- AI Gateway: bật logging/anonymization; không đẩy PII không cần thiết.

## 8) UX Requirements
- Sidebar width cố định (VD 360–420px), bố cục giống chatgpt.com: list messages, composer, actions.
- shadcn/ui components: Sidebar/Resizable (nếu cần), ScrollArea, DropdownMenu, Dialog/AlertDialog, Avatar, Badge, Skeleton.
- Tối ưu virtualization cho inbox dài; clamp text 2 dòng.
- Trạng thái: loading/streaming, lỗi, empty.
- Accessibility: focus ring, aria labels, keyboard submit.
- i18n sớm (vi/ en), nhưng code đặt tên tiếng Anh.

## 9) Data Model (khái quát)
- Account { id, email, picture, tokensRef }
- Conversation { id, accountId, title, createdAt }
- Message { id, conversationId, role(user|assistant|tool), content, attachments?, createdAt }
- Memory { accountId, kv } (vector/rag optional later)
- ScheduledJob { id, accountId, draftId?, payload, runAt, status }

## 10) MCP Tool Spec (khái quát)
- gmail.listThreads({ q, labelIds, pageToken, includeSpamTrash })
- gmail.getThread({ threadId })
- gmail.getMessage({ id, format })
- gmail.getAttachment({ messageId, attachmentId })
- gmail.createLabel({ name, color? })
- gmail.modifyLabels({ id, addLabelIds?, removeLabelIds? })
- gmail.markReadUnread({ id, unread: boolean })
- gmail.createDraft({ to, cc?, bcc?, subject, body, attachments? })
- gmail.updateDraft({ draftId, body?, attachments? })
- gmail.sendDraft({ draftId })
- gmail.sendMessage({ raw })
- gmail.deleteMessage({ id })
- summarize.thread({ threadId })
- classify.message({ id })
- schedule.send({ draftId|messageSpec, runAt })

Tất cả tools yêu cầu context account + policy gate cho destructive actions.

## 11) Acceptance Criteria (per phase)
- Phase 1 — Auth đa tài khoản
  - [ ] Thêm/Xoá/Chuyển tài khoản.
  - [ ] Token refresh, revoke.
  - [ ] Side panel hiển thị avatar/email.
- Phase 4.0 — Agent-first MVP (Read-only)
  - [ ] MCP server tại `/mcp` (streamable-http) chạy trên Worker.
  - [ ] Tools read-only hoạt động qua chat: `gmail.listThreads`, `gmail.getThread`, `gmail.getMessage`, `gmail.getAttachment`, `summarize.thread`.
  - [ ] AI Gateway streaming → Gemini 2.5 Flash Lite.
  - [ ] Durable Objects lưu state/memory; transcripts lưu KV/D1.
  - [ ] ElicitInput pipeline sẵn sàng cho xác nhận (HITL), dù chưa thực hiện hành động phá huỷ.
- Phase 4.1 — Action Tools + HITL
  - [ ] `gmail.modifyLabels`, `gmail.markReadUnread`, `gmail.createLabel` thực thi qua chat.
  - [ ] Human-in-the-loop: mọi thao tác thay đổi trạng thái yêu cầu confirm trước khi chạy.
- Phase 4.2 — Compose & Send (Chat-driven)
  - [ ] MIME builder (server) + `createDraft`, `updateDraft`, `sendDraft`, `sendMessage`.
  - [ ] Confirm gửi trước khi thực hiện; attachments cơ bản.
- Phase 4.3 — Scheduling (Cloudflare Workflows)
  - [ ] `schedule.send` orchestration bằng Workflows; gửi đúng giờ sau confirm.
- Phase 5 — Chat UI & Conversation Management
  - [ ] Streaming mượt; tool call traces hiển thị rõ; retry/copy.
  - [ ] Conversation persistence per-account; quick actions (Summarize inbox/thread, Draft reply, Label thread).

## 12) Dependencies & Versions (ưu tiên latest, 2025)
- bun, vite, react, react-dom, @crxjs/vite-plugin, tailwindcss v4, shadcn/ui, @radix-ui/*, zod, hono, agents (Cloudflare Agents SDK), hono-agents (nếu dùng), @modelcontextprotocol/sdk, dayjs/date-fns, @tanstack/react-virtual (optional), mime, postal-mime (server), undici/fetch.

## 13) Risks & Mitigations
- OAuth restrictions Google (app verification): dùng scopes tối thiểu, chuẩn bị màn hình consent.
 - Gmail schedule không native API: dùng draft + Cloudflare Workflows để chờ/gửi đúng giờ (orchestrate).
- Quota Gmail/AI: dùng AI Gateway với cache/rate limit, exponential backoff.

## 14) Success Metrics
- Thời gian thao tác email giảm (x%), tỉ lệ auto-draft được chấp nhận (x%), lỗi gửi/xóa sau khi confirm ~0.

## 15) References
- Cloudflare Agents + MCP: https://github.com/cloudflare/agents
- AI Gateway Universal Endpoint: https://developers.cloudflare.com/ai-gateway/
- CRXJS Vite plugin: https://github.com/crxjs/chrome-extension-tools
- shadcn/ui: https://ui.shadcn.com/
- Gmail API: https://developers.google.com/workspace/gmail/api
- Gemini API: https://ai.google.dev/gemini-api
