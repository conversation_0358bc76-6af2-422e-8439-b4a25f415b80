export const BACKEND_BASE_URL = (import.meta as any).env?.VITE_BACKEND_BASE_URL || 'https://ai-email-agent-worker.vudinhhiep3004.workers.dev'

export const BASE_SCOPES = [
  'openid',
  'email',
  'profile',
  'https://www.googleapis.com/auth/userinfo.email',
]

// For Phase 4.0 (read-only Gmail tools), include gmail.readonly
export const DEFAULT_SCOPES = [
  ...BASE_SCOPES,
  'https://www.googleapis.com/auth/gmail.readonly',
]

export const OAUTH_PROMPT = 'consent'
export const OAUTH_ACCESS_TYPE = 'offline'
export const OAUTH_INCLUDE_GRANTED_SCOPES = 'true'
